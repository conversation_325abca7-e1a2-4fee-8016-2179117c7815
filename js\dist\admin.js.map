{"version": 3, "file": "admin.js", "sources": ["../src/admin/components/SettingsPage.tsx", "../src/admin/index.js"], "sourcesContent": ["import app from 'flarum/admin/app';\nimport ExtensionPage from 'flarum/admin/components/ExtensionPage';\nimport type Mithril from 'mithril';\n\nexport default class SettingsPage extends ExtensionPage {\n  oninit(vnode: Mithril.Vnode<any, this>): void {\n    super.oninit(vnode);\n  }\n\n  content(): JSX.Element {\n    return (\n      <div className=\"ExtensionPage-settings\">\n        <div className=\"container\">\n          {this.buildSettingComponent({\n            type: 'switch',\n            setting: 'moneyTransfer.moneyTransferClient1Customization',\n            label: app.translator.trans('wusong8899-transfer-money.admin.transfer-money-client-customization'),\n            help: app.translator.trans('wusong8899-transfer-money.admin.transfer-money-client-customization-help'),\n          })}\n\n          {this.buildSettingComponent({\n            type: 'string',\n            setting: 'moneyTransfer.moneyTransferTimeZone',\n            label: app.translator.trans('wusong8899-transfer-money.admin.transfer-money-timezone'),\n            help: app.translator.trans('wusong8899-transfer-money.admin.transfer-money-timezone-help'),\n            placeholder: app.translator.trans('wusong8899-transfer-money.admin.transfer-money-timezone-default'),\n          })}\n\n          <div className=\"Form-group\">{this.submitButton()}</div>\n        </div>\n      </div>\n    );\n  }\n}\n\n", "import SettingsPage from './components/SettingsPage';\r\n\r\napp.initializers.add('wusong8899-money-transfer', () => {\r\n  app.extensionData.for('wusong8899-money-transfer')\r\n  .registerPage(SettingsPage)\r\n  .registerPermission(\r\n    {\r\n      icon: 'fas fa-exchange-alt',\r\n      label: app.translator.trans('wusong8899-transfer-money.admin.permission.allow_use_transfer_money'),\r\n      permission: 'transferMoney.allowUseTranferMoney',\r\n    },\r\n    'moderate',\r\n    90\r\n  )\r\n});\r\n"], "names": ["SettingsPage", "ExtensionPage", "vnode", "app"], "mappings": "4BAIA,MAAqBA,UAAqBC,CAAc,CACtD,OAAOC,EAAuC,CAC5C,MAAM,OAAOA,CAAK,CACpB,CAEA,SAAuB,CACrB,OACE,EAAC,OAAI,UAAU,wBAAA,IACZ,MAAA,CAAI,UAAU,aACZ,KAAK,sBAAsB,CAC1B,KAAM,SACN,QAAS,kDACT,MAAOC,EAAI,WAAW,MAAM,qEAAqE,EACjG,KAAMA,EAAI,WAAW,MAAM,0EAA0E,CAAA,CACtG,EAEA,KAAK,sBAAsB,CAC1B,KAAM,SACN,QAAS,sCACT,MAAOA,EAAI,WAAW,MAAM,yDAAyD,EACrF,KAAMA,EAAI,WAAW,MAAM,8DAA8D,EACzF,YAAaA,EAAI,WAAW,MAAM,iEAAiE,CAAA,CACpG,EAED,EAAC,MAAA,CAAI,UAAU,cAAc,KAAK,aAAA,CAAe,CACnD,CACF,CAEJ,CACF,CC/BA,IAAI,aAAa,IAAI,4BAA6B,IAAM,CACtD,IAAI,cAAc,IAAI,2BAA2B,EAChD,aAAaH,CAAY,EACzB,mBACC,CACE,KAAM,sBACN,MAAO,IAAI,WAAW,MAAM,qEAAqE,EACjG,WAAY,oCAClB,EACI,WACA,EACJ,CACA,CAAC"}