import app from 'flarum/forum/app';
import Modal from 'flarum/common/components/Modal';
import Button from 'flarum/common/components/Button';
import SearchState from 'flarum/forum/states/SearchState';
import ItemList from 'flarum/common/utils/ItemList';
import Stream from 'flarum/common/utils/Stream';
import Alert from 'flarum/common/components/Alert';
import type Mithril from 'mithril';

import TransferMoneySearchModal from './transfer-money-search-modal';
import TransferMoneySuccessModal from './transfer-money-success-modal';

export default class TransferMoneyModal extends Modal {
  static isDismissible = false;

  selected!: Stream<ItemList<unknown>>;
  selectedUsers!: Record<string, number>;
  moneyName!: string;
  recipientSearch!: SearchState;
  needMoney!: Stream<number>;

  oninit(vnode: Mithril.Vnode<unknown, this>): void {
    super.oninit(vnode);
    this.selected = Stream(new ItemList());
    this.selectedUsers = {};
    this.moneyName = app.forum.attribute('antoinefr-money.moneyname') || '[money]';

    const targetUser = (this.attrs as unknown as { user?: unknown }).user;
    if (targetUser) {
      this.selected().add('users:' + targetUser.id(), targetUser);
      this.selectedUsers[targetUser.id()] = 1;
    }

    this.recipientSearch = new SearchState();
    this.needMoney = Stream(0);
  }

  className(): string {
    return 'Modal--small';
  }

  title(): string {
    return app.translator.trans('wusong8899-transfer-money.forum.transfer-money');
  }

  content(): Mithril.Children {
    return (
      <div className="Modal-body">
        <div className="Form">
          <div style="padding-bottom:20px;" className="TransferMoneyModal-form">
            {TransferMoneySearchModal.component({
              state: this.recipientSearch,
              selected: this.selected,
              selectedUsers: this.selectedUsers,
              needMoney: this.needMoney,
              callback: function () {
                m.redraw();
              },
            })}
          </div>

          <div className="Form-group">
            <label>
              {app.translator.trans('wusong8899-transfer-money.forum.current-money-amount')}
              {this.moneyName.replace('[money]', app.session.user!.attribute('money'))}
            </label>
            <input
              id="moneyTransferInput"
              placeholder={app.translator.trans('wusong8899-transfer-money.forum.transfer-money-input-placeholder')}
              required
              className="FormControl"
              type="number"
              step="any"
              min="0"
              oninput={() => this.moneyTransferChanged()}
            />
            <div style="padding-top:10px">
              {app.translator.trans('wusong8899-transfer-money.forum.need-money-amount')}
              <span id="needMoneyContainer">{this.moneyName.replace('[money]', String(this.needMoney()))}</span>
            </div>
          </div>

          <div className="Form-group">
            <label>{app.translator.trans('wusong8899-transfer-money.forum.transfer-money-notes')}</label>
            <textarea id="moneyTransferNotesInput" maxlength="255" className="FormControl" />
          </div>

          <div className="Form-group" style="text-align: center;">
            {Button.component(
              {
                className: 'Button Button--primary',
                type: 'submit',
                loading: this.loading,
              },
              app.translator.trans('wusong8899-transfer-money.forum.ok')
            )}
            &nbsp;
            {Button.component(
              {
                className: 'Button transferMoneyButton--gray',
                loading: this.loading,
                onclick: () => {
                  this.hide();

                  if (typeof (this.attrs as any).callback === 'function') {
                    (this.attrs as any).callback();
                  }
                },
              },
              app.translator.trans('wusong8899-transfer-money.forum.cancel')
            )}
          </div>
        </div>
      </div>
    );
  }

  getTotalNeedMoney() {
    let moneyTransferValue = Number.parseFloat((globalThis as any).$('#moneyTransferInput').val());

    if (Number.isNaN(moneyTransferValue)) {
      moneyTransferValue = 0;
    }

    return Object.keys(this.selectedUsers).length * moneyTransferValue;
  }

  moneyTransferChanged() {
    const totalNeedMoney = this.getTotalNeedMoney();
    const totalNeedMoneyText = this.moneyName.replace('[money]', String(totalNeedMoney));
    (globalThis as any).$('#needMoneyContainer').text(totalNeedMoneyText);
  }

  onsubmit(e: Event) {
    e.preventDefault();
    const userMoney = app.session.user!.attribute('money');
    const moneyTransferValue = Number.parseFloat((globalThis as any).$('#moneyTransferInput').val());
    const moneyTransferValueTotal = this.getTotalNeedMoney();
    const moneyTransferNotesValue = (globalThis as any).$('#moneyTransferNotesInput').val();

    if (moneyTransferValueTotal > userMoney) {
      app.alerts.show(Alert, { type: 'error' }, app.translator.trans('wusong8899-transfer-money.forum.transfer-error-insufficient-fund'));
      return;
    }

    if (Object.keys(this.selectedUsers).length === 0) {
      app.alerts.show(
        Alert,
        { type: 'error' },
        app.translator.trans('wusong8899-transfer-money.forum.transfer-error-no-target-user-selected')
      );
      return;
    }

    if (moneyTransferValue > 0) {
      const moneyTransferData = {
        moneyTransfer: moneyTransferValue,
        moneyTransferNotes: moneyTransferNotesValue,
        selectedUsers: JSON.stringify(Object.keys(this.selectedUsers)),
      };

      this.loading = true;

      app.store
        .createRecord('transferMoney')
        .save(moneyTransferData)
        .then((payload: any) => {
          app.store.pushPayload(payload);
          app.modal.show(TransferMoneySuccessModal);
          this.loading = false;

          if (typeof (this.attrs as any).callback === 'function') {
            (this.attrs as any).callback();
          }
        })
        .catch(() => {
          this.loading = false;
        });
    }
  }
}

