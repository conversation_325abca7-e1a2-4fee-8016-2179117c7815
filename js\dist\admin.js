(function(n,e){"use strict";class t extends e{oninit(a){super.oninit(a)}content(){return m("div",{className:"ExtensionPage-settings"},m("div",{className:"container"},this.buildSettingComponent({type:"switch",setting:"moneyTransfer.moneyTransferClient1Customization",label:n.translator.trans("wusong8899-transfer-money.admin.transfer-money-client-customization"),help:n.translator.trans("wusong8899-transfer-money.admin.transfer-money-client-customization-help")}),this.buildSettingComponent({type:"string",setting:"moneyTransfer.moneyTransferTimeZone",label:n.translator.trans("wusong8899-transfer-money.admin.transfer-money-timezone"),help:n.translator.trans("wusong8899-transfer-money.admin.transfer-money-timezone-help"),placeholder:n.translator.trans("wusong8899-transfer-money.admin.transfer-money-timezone-default")}),m("div",{className:"Form-group"},this.submitButton())))}}app.initializers.add("wusong8899-money-transfer",()=>{app.extensionData.for("wusong8899-money-transfer").registerPage(t).registerPermission({icon:"fas fa-exchange-alt",label:app.translator.trans("wusong8899-transfer-money.admin.permission.allow_use_transfer_money"),permission:"transferMoney.allowUseTranferMoney"},"moderate",90)})})(flarum.core.compat["admin/app"],flarum.core.compat["admin/components/ExtensionPage"]);
//# sourceMappingURL=admin.js.map

module.exports={};