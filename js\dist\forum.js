(function(g,O,j,S,N,l,s,k,T,_,R,H,z,G,D,B,J,V,q,K,Q,X,Y,F,L,v,Z,A,P,W){"use strict";class E extends l{}Object.assign(E.prototype,{id:l.attribute("id"),transferMoney:l.attribute("transfer_money_value"),notes:l.attribute("notes"),assignedAt:l.attribute("assigned_at"),fromUser:l.hasOne("fromUser"),targetUser:l.hasOne("targetUser")});class ee{constructor(){this.results=[]}async search(e){if(!e||e.length<3){this.results=[];return}const t=await app.store.find("users",{filter:{q:e},page:{limit:5}});this.results=t}view(e){const t=this.results;return!t||!t.length?[]:t.map(n=>m("li",{className:"SearchResult","data-index":`users:${n.id()}`},m("a",{className:"SearchResult",tabindex:"-1"},B(n)," ",D(n),m("span",{className:"SearchResult-excerpt"},J(n.username(),e)))))}}class te extends G{constructor(){super(...arguments),this.doSearch=!1}oninit(e){super.oninit(e),this.inputUuid=Math.random().toString(36).substring(2)}oncreate(e){super.oncreate(e);const t=this;this.$(".Search-results").on("click",()=>{const n=this.$(".SearchResult.active");t.addRecipient(n.data("index")),t.$(".RecipientsInput").focus()}),this.$(".Search-results").on("touchstart",n=>{const o=this.$(n.target.parentNode);t.addRecipient(o.data("index")),t.$(".RecipientsInput").focus()}),window.$(".RecipientsInput").on("input",()=>{clearTimeout(this.typingTimer),this.doSearch=!1,this.typingTimer=window.setTimeout(()=>{this.doSearch=!0,m.redraw()},900)}).on("keydown",()=>{clearTimeout(this.typingTimer)}),super.oncreate(e)}view(){typeof this.searchState.getValue()>"u"&&this.searchState.setValue("");const e=this.searchState.getValue()&&this.searchState.getValue().length>=3;this.sources||(this.sources=this.sourceItems().toArray());const t=this.attrs.selected().toArray();return m("div",{role:"search",className:"Search"},m("div",{className:"RecipientsInput-selected RecipientsLabel","aria-live":"polite"},m("div",{style:"padding-bottom:10px;font-weight:bold;font-size: 14px;color: var(--text-color);"},s.translator.trans("wusong8899-transfer-money.forum.transfer-money-to-user")),t.length===0&&m("div",{style:"height:34px;cursor: default !important;",class:"transferSearchUserContainer"},s.translator.trans("wusong8899-transfer-money.forum.transfer-money-no-user-selected")),this.attrs.selected().toArray().map(n=>{const o=D(n),r=B(n),i=n.data.id;return this.attrs.selectedUsers[i]=1,m("div",{class:"transferSearchUserContainer",onclick:c=>this.removeRecipient(n,c)},m("span",{class:"transferSearchUser"},r)," ",o)})),m("div",{className:"Form-group"},m("label",{htmlFor:`transfer-money-user-search-input-${this.inputUuid}`},s.translator.trans("wusong8899-transfer-money.forum.transfer-money-search-user")),m("div",{className:"AddRecipientModal-form-input Search-input"},m("input",{id:`transfer-money-user-search-input-${this.inputUuid}`,className:V("RecipientsInput","FormControl",{open:!!this.searchState.getValue(),focused:!!this.searchState.getValue(),active:!!this.searchState.getValue(),loading:!!this.loadingSources}),type:"search",placeholder:q(s.translator.trans("wusong8899-transfer-money.forum.transfer-money-search-user-placeholder")),value:this.searchState.getValue(),oninput:n=>this.searchState.setValue(n.target.value),onfocus:()=>this.hasFocus=!0,onblur:()=>this.hasFocus=!1}),m("ul",{className:V("Dropdown-menu","Search-results","fade",{in:!!e})},this.doSearch?this.sources.map(n=>n.view(this.searchState.getValue())):K.component({size:"tiny",className:"Button Button--icon Button--link"})))))}sourceItems(){const e=new R;return e.add("users",new ee),e}addRecipient(e){const t=e.split(":"),n=t[0],o=t[1],r=this.findRecipient(n,o),i=r.data.id;this.attrs.selected().add(e,r),this.attrs.selectedUsers[i]=1,this.searchState.clear(),this.attrs.needMoney(this.getNeedMoney()),this.attrs.callback()}removeRecipient(e,t){t.preventDefault();const n=e.data.id;delete this.attrs.selectedUsers[n],this.attrs.selected().remove("users"+":"+e.id()),this.attrs.needMoney(this.getNeedMoney()),this.attrs.callback()}getNeedMoney(){return window.$("#moneyTransferInput").val()*Object.keys(this.attrs.selectedUsers).length}findRecipient(e,t){return s.store.getById(e,t)}}const C=class C extends k{oninit(e){super.oninit(e)}className(){return"Modal--small"}title(){return s.translator.trans("wusong8899-transfer-money.forum.transfer-money-success")}content(){return[m("div",{className:"Modal-body"},m("div",{style:"text-align:center"},T.component({style:"width:66px",className:"Button Button--primary",onclick:()=>{location.reload()}},s.translator.trans("wusong8899-transfer-money.forum.ok"))))]}};C.isDismissible=!1;let x=C;const M=class M extends k{oninit(e){super.oninit(e),this.selected=H(new R),this.selectedUsers={},this.moneyName=s.forum.attribute("antoinefr-money.moneyname")||"[money]";const t=this.attrs.user;t&&(this.selected().add("users:"+t.id(),t),this.selectedUsers[t.id()]=1),this.recipientSearch=new _,this.needMoney=H(0)}className(){return"Modal--small"}title(){return s.translator.trans("wusong8899-transfer-money.forum.transfer-money")}content(){return m("div",{className:"Modal-body"},m("div",{className:"Form"},m("div",{style:"padding-bottom:20px;",className:"TransferMoneyModal-form"},te.component({state:this.recipientSearch,selected:this.selected,selectedUsers:this.selectedUsers,needMoney:this.needMoney,callback:function(){m.redraw()}})),m("div",{className:"Form-group"},m("label",null,s.translator.trans("wusong8899-transfer-money.forum.current-money-amount"),this.moneyName.replace("[money]",s.session.user.attribute("money"))),m("input",{id:"moneyTransferInput",placeholder:s.translator.trans("wusong8899-transfer-money.forum.transfer-money-input-placeholder"),required:!0,className:"FormControl",type:"number",step:"any",min:"0",oninput:()=>this.moneyTransferChanged()}),m("div",{style:"padding-top:10px"},s.translator.trans("wusong8899-transfer-money.forum.need-money-amount"),m("span",{id:"needMoneyContainer"},this.moneyName.replace("[money]",String(this.needMoney()))))),m("div",{className:"Form-group"},m("label",null,s.translator.trans("wusong8899-transfer-money.forum.transfer-money-notes")),m("textarea",{id:"moneyTransferNotesInput",maxlength:"255",className:"FormControl"})),m("div",{className:"Form-group",style:"text-align: center;"},T.component({className:"Button Button--primary",type:"submit",loading:this.loading},s.translator.trans("wusong8899-transfer-money.forum.ok"))," ",T.component({className:"Button transferMoneyButton--gray",loading:this.loading,onclick:()=>{this.hide(),typeof this.attrs.callback=="function"&&this.attrs.callback()}},s.translator.trans("wusong8899-transfer-money.forum.cancel")))))}getTotalNeedMoney(){let e=Number.parseFloat(globalThis.$("#moneyTransferInput").val());return Number.isNaN(e)&&(e=0),Object.keys(this.selectedUsers).length*e}moneyTransferChanged(){const e=this.getTotalNeedMoney(),t=this.moneyName.replace("[money]",String(e));globalThis.$("#needMoneyContainer").text(t)}onsubmit(e){e.preventDefault();const t=s.session.user.attribute("money"),n=Number.parseFloat(globalThis.$("#moneyTransferInput").val()),o=this.getTotalNeedMoney(),r=globalThis.$("#moneyTransferNotesInput").val();if(o>t){s.alerts.show(z,{type:"error"},s.translator.trans("wusong8899-transfer-money.forum.transfer-error-insufficient-fund"));return}if(Object.keys(this.selectedUsers).length===0){s.alerts.show(z,{type:"error"},s.translator.trans("wusong8899-transfer-money.forum.transfer-error-no-target-user-selected"));return}if(n>0){const i={moneyTransfer:n,moneyTransferNotes:r,selectedUsers:JSON.stringify(Object.keys(this.selectedUsers))};this.loading=!0,s.store.createRecord("transferMoney").save(i).then(c=>{s.store.pushPayload(c),s.modal.show(x),this.loading=!1,typeof this.attrs.callback=="function"&&this.attrs.callback()}).catch(()=>{this.loading=!1})}}};M.isDismissible=!1;let f=M;class ne extends Q{icon(){return"fas fa-money-bill"}href(){const e=s.session.user,t=e?e.username():"";return s.route("user.transferHistory",{username:t})}content(){const e=this.attrs.notification.fromUser();return s.translator.trans("wusong8899-transfer-money.forum.notifications.user-transfer-money-to-you",{user:e})}excerpt(){const e=this.attrs.notification.subject(),t=e&&e.attribute?e.attribute("transfer_money_value"):void 0,n=e&&e.attribute?e.attribute("id"):void 0,r=(s.forum.attribute("antoinefr-money.moneyname")||"[money]").replace("[money]",t);return s.translator.trans("wusong8899-transfer-money.forum.notifications.user-transfer-money-details",{cost:r,id:n})}}class se extends L{view(){const{transferHistory:e}=this.attrs,t=app.session.user.id(),n=e.attribute("from_user_id"),o=e.assignedAt(),r=e.fromUser(),i=e.targetUser(),c=e.transferMoney(),w=e.notes(),I=w||app.translator.trans("wusong8899-transfer-money.forum.transfer-list-transfer-notes-none"),u=e.id(),d=app.translator.trans(t==n?"wusong8899-transfer-money.forum.transfer-money-out":"wusong8899-transfer-money.forum.transfer-money-in"),p=t==n?"color:red":"color:green",h=(app.forum.attribute("antoinefr-money.moneyname")||"[money]").replace("[money]",c);return m("div",{className:"transferHistoryContainer"},m("div",{style:"padding-top: 5px;"},m("b",null,app.translator.trans("wusong8899-transfer-money.forum.transfer-list-type"),": "),m("span",{style:p},d)," | ",m("b",null,app.translator.trans("wusong8899-transfer-money.forum.transfer-list-assign-at"),": "),o),m("div",{style:"padding-top: 5px;"},m("b",null,app.translator.trans("wusong8899-transfer-money.forum.transfer-list-id"),": "),u," | ",m("b",null,app.translator.trans("wusong8899-transfer-money.forum.transfer-list-from-user"),": "),m(A,{href:r?app.route.user(r):"#",className:"transferHistoryUser",style:"color:var(--heading-color)"},P(r)," ",W(r))," | ",m("b",null,app.translator.trans("wusong8899-transfer-money.forum.transfer-list-target-user"),": "),m(A,{href:i?app.route.user(i):"#",className:"transferHistoryUser",style:"color:var(--heading-color)"},P(i)," ",W(i))," | ",m("b",null,app.translator.trans("wusong8899-transfer-money.forum.transfer-list-transfer-amount"),": "),h," ",w&&m("span",null,"| ",m("b",null,app.translator.trans("wusong8899-transfer-money.forum.transfer-list-transfer-notes"),": "),I)))}}class re extends L{oninit(e){super.oninit(e),this.loading=!0,this.moreResults=!1,this.transferHistory=[],this.user=this.attrs.params.user,this.loadResults()}view(){if(this.loading){const e=Z.component({size:"large"});return m("div",null,e)}return m("div",null,m("div",{style:"padding-bottom:10px; font-size: 24px;font-weight: bold;"},v.translator.trans("wusong8899-transfer-money.forum.transfer-money-history")),m("ul",{style:"margin: 0;padding: 0;list-style-type: none;position: relative;"},this.transferHistory.map(e=>m("li",{style:"padding-top:5px",key:e.id(),"data-id":e.id()},se.component({transferHistory:e})))),!this.loading&&this.transferHistory.length===0&&m("div",null,m("div",{style:"font-size:1.4em;color: var(--muted-more-color);text-align: center;height: 300px;line-height: 100px;"},v.translator.trans("wusong8899-transfer-money.forum.transfer-list-empty"))),this.hasMoreResults()&&m("div",{style:"text-align:center;padding:20px"},m(N,{className:"Button Button--primary",disabled:this.loading,loading:this.loading,onclick:()=>this.loadMore()},v.translator.trans("wusong8899-transfer-money.forum.transfer-list-load-more"))))}loadMore(){this.loading=!0,this.loadResults(this.transferHistory.length)}parseResults(e){return this.moreResults=!!e.payload.links&&!!e.payload.links.next,[].push.apply(this.transferHistory,e),this.loading=!1,m.redraw(),e}hasMoreResults(){return this.moreResults}loadResults(e=0){return v.store.find("transferHistory",{filter:{user:this.user.id()},page:{offset:e}}).catch(()=>{}).then(this.parseResults.bind(this))}}class ae extends F{oninit(e){super.oninit(e),this.loadUser(m.route.param("username"))}content(){return m("div",{className:"TransferHistoryPage"},re.component({params:{user:this.user}}))}}function oe(){app.routes["user.transferHistory"]={component:ae,path:"/u/:username/transferHistory"},X.extend(F.prototype,"navItems",function(e){if(app.session.user){const t=app.session.user.id(),n=this.user.id();t===n&&e.add("transferMoney",Y.component({href:app.route("user.transferHistory",{username:this.user.username()}),icon:"fas fa-money-bill"},[app.translator.trans("wusong8899-transfer-money.forum.transfer-money-history")]),10)}})}const ie=10;function le(){if(app.forum.attribute("moneyTransferClient1Customization")!=="1")return;let e=document.getElementById("transferMoneyLabelContainer");e!==null&&$(e).remove()}function me(a,e){const t=$("#drawer").css("visibility")==="hidden",n=app.forum.attribute("moneyTransferClient1Customization");if(t===!1||n!=="1")return;$("#content .IndexPage-nav .item-nav").css("display","none"),$("#content .IndexPage-nav .item-newDiscussion").remove();let o=setInterval(function(){if(a.dom&&(clearInterval(o),a.dom!==void 0)){$("#content .IndexPage-nav .item-nav").css("display","none"),$("#content .IndexPage-nav .item-newDiscussion").remove();let r=document.getElementById("transferMoneyLabelContainer");if(r!==null)return;$("#content .IndexPage-nav .item-nav .ButtonGroup").removeClass("App-titleControl"),$("#content .IndexPage-nav .item-nav .ButtonGroup button").addClass("Button--link");let i=$("#content .IndexPage-nav .item-nav").clone();i.length>0&&($("#itemNavClone").remove(),$(i).attr("id","itemNavClone"),$(i).css("display",""),$("#header-secondary .Header-controls").prepend(i));const c=document.getElementById("app-navigation"),I=(app.forum.attribute("antoinefr-money.moneyname")||"[money]").replace("[money]",app.session.user.attribute("money"));r=document.createElement("div"),r.id="transferMoneyLabelContainer",r.className="clientCustomizeWithdrawalButtonContainer";const u=document.createElement("div");u.className="clientCustomizeWithdrawalHeaderItems clientCustomizeWithdrawalHeaderTotalMoney";const d=document.createElement("div");d.innerHTML='<span style="font-size:16px;"><i class="fab fa-bitcoin" style="padding-right: 8px;color: gold;"></i></span>'+I,d.className="clientCustomizeWithdrawalHeaderText";const p=document.createElement("div");p.innerHTML='<i class="fas fa-wallet"></i>',p.className="clientCustomizeWithdrawalHeaderIcon",u.appendChild(d),u.appendChild(p);const y=document.createElement("div");y.innerHTML=app.translator.trans("wusong8899-transfer-money.forum.withdrawal"),y.className="clientCustomizeWithdrawalHeaderItems clientCustomizeWithdrawalHeaderWithdrawal",$(y).click(function(){app.modal.show(f)});const h=document.createElement("div");h.className="clientCustomizeWithdrawalHeaderItems clientCustomizeWithdrawalHeaderUser";const b=$("#header-secondary .item-session .SessionDropdown").clone();$(b).attr("id","avatarClone"),$(b).addClass("App-primaryControl"),$(h).html(b);let U="";$(b).on("click",function(){U=U===""?"none":"",$("#content .IndexPage-nav").css("display",U)}),r.appendChild(u),r.appendChild(y),r.appendChild(h),c.appendChild(r)}},ie)}function ce(){g.extend(S.prototype,"view",function(a){if(!app.session.user)return;const e=app.current.get("routeName");e&&(e!=="tags"?le():me(a,this.attrs.user))})}app.initializers.add("wusong8899-money-transfer",()=>{app.store.models.transferMoney=E,app.notificationComponents.transferMoney=ne,oe(),ce(),g.extend(j.prototype,"notificationTypes",function(a){a.add("transferMoney",{name:"transferMoney",icon:"fas fa-dollar-sign",label:app.translator.trans("wusong8899-transfer-money.forum.receive-transfer-from-user")})}),g.extend(O,"moderationControls",(a,e)=>{const t=app.forum.attribute("allowUseTranferMoney");if(app.session.user&&t){const n=app.session.user.id(),o=e.id();n!==o&&a.add("transferMoney",N.component({icon:"fas fa-money-bill",onclick:()=>app.modal.show(f,{user:e})},app.translator.trans("wusong8899-transfer-money.forum.transfer-money")))}}),g.extend(S.prototype,"items",function(a){app.session.user&&a.add("transferMoney",N.component({icon:"fas fa-money-bill",onclick:()=>{app.modal.show(f)}},app.translator.trans("wusong8899-transfer-money.forum.transfer-money")),-1)})})})(flarum.core.compat.extend,flarum.core.compat["utils/UserControls"],flarum.core.compat["components/NotificationGrid"],flarum.core.compat["forum/components/SessionDropdown"],flarum.core.compat["components/Button"],flarum.core.compat["common/Model"],flarum.core.compat["forum/app"],flarum.core.compat["common/components/Modal"],flarum.core.compat["common/components/Button"],flarum.core.compat["forum/states/SearchState"],flarum.core.compat["common/utils/ItemList"],flarum.core.compat["common/utils/Stream"],flarum.core.compat["common/components/Alert"],flarum.core.compat["forum/components/Search"],flarum.core.compat["common/helpers/username"],flarum.core.compat["common/helpers/avatar"],flarum.core.compat["common/helpers/highlight"],flarum.core.compat["common/utils/classList"],flarum.core.compat["common/utils/extractText"],flarum.core.compat["common/components/LoadingIndicator"],flarum.core.compat["forum/components/Notification"],flarum.core.compat["common/extend"],flarum.core.compat["components/LinkButton"],flarum.core.compat["components/UserPage"],flarum.core.compat.Component,flarum.core.compat.app,flarum.core.compat["components/LoadingIndicator"],flarum.core.compat["components/Link"],flarum.core.compat["helpers/avatar"],flarum.core.compat["helpers/username"]);
//# sourceMappingURL=forum.js.map

module.exports={};