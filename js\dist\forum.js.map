{"version": 3, "file": "forum.js", "sources": ["../src/forum/model/TransferMoney.ts", "../src/forum/components/sources/UserSearchSource.tsx", "../src/forum/components/TransferMoneySearchModal.tsx", "../src/forum/components/TransferMoneySuccessModal.tsx", "../src/forum/components/TransferMoneyModal.tsx", "../src/forum/components/TransferMoneyNotification.ts", "../src/forum/components/TransferHistoryListItem.tsx", "../src/forum/components/TransferHistoryList.tsx", "../src/forum/components/TransferHistoryPage.tsx", "../src/forum/addTransferMoneyPage.ts", "../src/forum/addClient1CustomizationFeatures.ts", "../src/forum/index.ts"], "sourcesContent": ["import Model from 'flarum/common/Model';\n\n// Model for transfer money records\nexport default class Transfer<PERSON>oney extends Model {}\n\n// Map API attributes/relationships to model accessors\nObject.assign(TransferMoney.prototype, {\n  // id is provided by the API\n  id: Model.attribute<string | undefined>('id'),\n  transferMoney: Model.attribute<number>('transfer_money_value'),\n  notes: Model.attribute<string | null>('notes'),\n  assignedAt: Model.attribute<string>('assigned_at'),\n  fromUser: Model.hasOne('fromUser'),\n  targetUser: Model.hasOne('targetUser'),\n});\n\n", "import type Mithril from 'mithril';\nimport username from 'flarum/common/helpers/username';\nimport avatar from 'flarum/common/helpers/avatar';\nimport highlight from 'flarum/common/helpers/highlight';\n\nexport default class UserSearchSource {\n  private results: any[] = [];\n\n  async search(query: string): Promise<void> {\n    if (!query || query.length < 3) {\n      this.results = [];\n      return;\n    }\n\n    const results = await app.store.find('users', { filter: { q: query }, page: { limit: 5 } });\n    this.results = results;\n  }\n\n  view(query: string): Array<Mithril.Vnode> {\n    const results = this.results;\n\n    if (!results || !results.length) return [] as any;\n\n    return results.map((user: any) => (\n      <li className=\"SearchResult\" data-index={`users:${user.id()}`}>\n        <a className=\"SearchResult\" tabindex=\"-1\">\n          {avatar(user)} {username(user)}\n          <span className=\"SearchResult-excerpt\">{highlight(user.username(), query)}</span>\n        </a>\n      </li>\n    )) as any;\n  }\n}\n\n", "import app from 'flarum/forum/app';\nimport Search from 'flarum/forum/components/Search';\nimport UserSearchSource from './sources/UserSearchSource';\nimport ItemList from 'flarum/common/utils/ItemList';\nimport classList from 'flarum/common/utils/classList';\nimport extractText from 'flarum/common/utils/extractText';\nimport LoadingIndicator from 'flarum/common/components/LoadingIndicator';\nimport username from 'flarum/common/helpers/username';\nimport avatar from 'flarum/common/helpers/avatar';\nimport type Mithril from 'mithril';\n\nexport default class TransferMoneySearchModal extends Search<any> {\n  inputUuid!: string;\n  typingTimer?: number;\n  doSearch = false;\n\n  oninit(vnode: Mithril.Vnode<any, this>): void {\n    super.oninit(vnode);\n    this.inputUuid = Math.random().toString(36).substring(2);\n  }\n\n  oncreate(vnode: Mithril.VnodeDOM<any, this>): void {\n    super.oncreate(vnode);\n\n    const $search = this as any;\n\n    this.$('.Search-results').on('click', () => {\n      const target = this.$('.SearchResult.active');\n\n      $search.addRecipient(target.data('index'));\n      $search.$('.RecipientsInput').focus();\n    });\n\n    this.$('.Search-results').on('touchstart', (e: any) => {\n      const target = this.$(e.target.parentNode);\n\n      $search.addRecipient(target.data('index'));\n      $search.$('.RecipientsInput').focus();\n    });\n\n    (window as any).$('.RecipientsInput')\n      .on('input', () => {\n        clearTimeout(this.typingTimer);\n        this.doSearch = false;\n        this.typingTimer = window.setTimeout(() => {\n          this.doSearch = true;\n          m.redraw();\n        }, 900);\n      })\n      .on('keydown', () => {\n        clearTimeout(this.typingTimer);\n      });\n\n    super.oncreate(vnode);\n  }\n\n  view() {\n    if (typeof this.searchState.getValue() === 'undefined') {\n      this.searchState.setValue('');\n    }\n\n    const loading = this.searchState.getValue() && this.searchState.getValue().length >= 3;\n\n    if (!this.sources) {\n      this.sources = this.sourceItems().toArray();\n    }\n\n    const selectedUserArray = this.attrs.selected().toArray();\n\n    return (\n      <div role=\"search\" className=\"Search\">\n        <div className=\"RecipientsInput-selected RecipientsLabel\" aria-live=\"polite\">\n          <div style=\"padding-bottom:10px;font-weight:bold;font-size: 14px;color: var(--text-color);\">\n            {app.translator.trans('wusong8899-transfer-money.forum.transfer-money-to-user')}\n          </div>\n\n          {selectedUserArray.length === 0 && (\n            <div style=\"height:34px;cursor: default !important;\" class=\"transferSearchUserContainer\">\n              {app.translator.trans('wusong8899-transfer-money.forum.transfer-money-no-user-selected')}\n            </div>\n          )}\n\n          {this.attrs\n            .selected()\n            .toArray()\n            .map((recipient: any) => {\n              const userName = username(recipient);\n              const userAvatar = avatar(recipient);\n              const userID = recipient.data.id;\n              this.attrs.selectedUsers[userID] = 1;\n\n              return (\n                <div class=\"transferSearchUserContainer\" onclick={(e: MouseEvent) => this.removeRecipient(recipient, e)}>\n                  <span class=\"transferSearchUser\">{userAvatar}</span> {userName}\n                </div>\n              );\n            })}\n        </div>\n\n        <div className=\"Form-group\">\n          <label htmlFor={`transfer-money-user-search-input-${this.inputUuid}`}>\n            {app.translator.trans('wusong8899-transfer-money.forum.transfer-money-search-user')}\n          </label>\n\n          <div className=\"AddRecipientModal-form-input Search-input\">\n            <input\n              id={`transfer-money-user-search-input-${this.inputUuid}`}\n              className={classList('RecipientsInput', 'FormControl', {\n                open: !!this.searchState.getValue(),\n                focused: !!this.searchState.getValue(),\n                active: !!this.searchState.getValue(),\n                loading: !!this.loadingSources,\n              })}\n              type=\"search\"\n              placeholder={extractText(\n                app.translator.trans('wusong8899-transfer-money.forum.transfer-money-search-user-placeholder')\n              )}\n              value={this.searchState.getValue()}\n              oninput={(e: any) => this.searchState.setValue(e.target.value)}\n              onfocus={() => (this.hasFocus = true)}\n              onblur={() => (this.hasFocus = false)}\n            />\n            <ul className={classList('Dropdown-menu', 'Search-results', 'fade', { in: !!loading })}>\n              {!this.doSearch\n                ? LoadingIndicator.component({ size: 'tiny', className: 'Button Button--icon Button--link' })\n                : (this.sources as any).map((source: any) => source.view(this.searchState.getValue()))}\n            </ul>\n          </div>\n        </div>\n      </div>\n    );\n  }\n\n  sourceItems() {\n    const items = new ItemList();\n    items.add('users', new UserSearchSource());\n    return items;\n  }\n\n  addRecipient(value: string) {\n    const values = value.split(':');\n    const type = values[0];\n    const id = values[1];\n    const recipient = this.findRecipient(type, id);\n    const userID = recipient.data.id;\n\n    this.attrs.selected().add(value, recipient);\n    this.attrs.selectedUsers[userID] = 1;\n    this.searchState.clear();\n    this.attrs.needMoney(this.getNeedMoney());\n    this.attrs.callback();\n  }\n\n  removeRecipient(recipient: any, e: Event) {\n    e.preventDefault();\n\n    const userID = recipient.data.id;\n    delete this.attrs.selectedUsers[userID];\n\n    const type = 'users';\n    this.attrs.selected().remove(type + ':' + recipient.id());\n    this.attrs.needMoney(this.getNeedMoney());\n    this.attrs.callback();\n  }\n\n  getNeedMoney() {\n    const moneyTransferValue = (window as any).$('#moneyTransferInput').val();\n    return moneyTransferValue * Object.keys(this.attrs.selectedUsers).length;\n  }\n\n  findRecipient(store: string, id: string) {\n    return app.store.getById(store, id);\n  }\n}\n\n", "import app from 'flarum/forum/app';\nimport Modal from 'flarum/common/components/Modal';\nimport Button from 'flarum/common/components/Button';\nimport type Mithril from 'mithril';\n\nexport default class TransferMoneySuccessModal extends Modal {\n  static isDismissible = false;\n\n  oninit(vnode: Mithril.Vnode<any, this>): void {\n    super.oninit(vnode);\n  }\n\n  className(): string {\n    return 'Modal--small';\n  }\n\n  title() {\n    return app.translator.trans('wusong8899-transfer-money.forum.transfer-money-success');\n  }\n\n  content() {\n    return [\n      <div className=\"Modal-body\">\n        <div style=\"text-align:center\">\n          {Button.component(\n            {\n              style: 'width:66px',\n              className: 'Button Button--primary',\n              onclick: () => {\n                location.reload();\n              },\n            },\n            app.translator.trans('wusong8899-transfer-money.forum.ok')\n          )}\n        </div>\n      </div>,\n    ];\n  }\n}\n\n", "import app from 'flarum/forum/app';\nimport Modal from 'flarum/common/components/Modal';\nimport Button from 'flarum/common/components/Button';\nimport SearchState from 'flarum/forum/states/SearchState';\nimport ItemList from 'flarum/common/utils/ItemList';\nimport Stream from 'flarum/common/utils/Stream';\nimport Alert from 'flarum/common/components/Alert';\nimport type Mithril from 'mithril';\n\nimport TransferMoneySearchModal from './TransferMoneySearchModal';\nimport TransferMoneySuccessModal from './TransferMoneySuccessModal';\n\nexport default class TransferMoneyModal extends Modal {\n  static isDismissible = false;\n\n  selected!: Stream<ItemList<any>>;\n  selectedUsers!: Record<string, number>;\n  moneyName!: string;\n  recipientSearch!: SearchState;\n  needMoney!: Stream<number>;\n\n  oninit(vnode: Mithril.Vnode<any, this>): void {\n    super.oninit(vnode);\n    this.selected = Stream(new ItemList());\n    this.selectedUsers = {};\n    this.moneyName = app.forum.attribute('antoinefr-money.moneyname') || '[money]';\n\n    const targetUser = (this.attrs as any).user;\n    if (targetUser) {\n      this.selected().add('users:' + targetUser.id(), targetUser);\n      this.selectedUsers[targetUser.id()] = 1;\n    }\n\n    this.recipientSearch = new SearchState();\n    this.needMoney = Stream(0);\n  }\n\n  className(): string {\n    return 'Modal--small';\n  }\n\n  title() {\n    return app.translator.trans('wusong8899-transfer-money.forum.transfer-money');\n  }\n\n  content() {\n    return (\n      <div className=\"Modal-body\">\n        <div className=\"Form\">\n          <div style=\"padding-bottom:20px;\" className=\"TransferMoneyModal-form\">\n            {TransferMoneySearchModal.component({\n              state: this.recipientSearch,\n              selected: this.selected,\n              selectedUsers: this.selectedUsers,\n              needMoney: this.needMoney,\n              callback: function () {\n                m.redraw();\n              },\n            })}\n          </div>\n\n          <div className=\"Form-group\">\n            <label>\n              {app.translator.trans('wusong8899-transfer-money.forum.current-money-amount')}\n              {this.moneyName.replace('[money]', app.session.user!.attribute('money'))}\n            </label>\n            <input\n              id=\"moneyTransferInput\"\n              placeholder={app.translator.trans('wusong8899-transfer-money.forum.transfer-money-input-placeholder')}\n              required\n              className=\"FormControl\"\n              type=\"number\"\n              step=\"any\"\n              min=\"0\"\n              oninput={() => this.moneyTransferChanged()}\n            />\n            <div style=\"padding-top:10px\">\n              {app.translator.trans('wusong8899-transfer-money.forum.need-money-amount')}\n              <span id=\"needMoneyContainer\">{this.moneyName.replace('[money]', String(this.needMoney()))}</span>\n            </div>\n          </div>\n\n          <div className=\"Form-group\">\n            <label>{app.translator.trans('wusong8899-transfer-money.forum.transfer-money-notes')}</label>\n            <textarea id=\"moneyTransferNotesInput\" maxlength=\"255\" className=\"FormControl\" />\n          </div>\n\n          <div className=\"Form-group\" style=\"text-align: center;\">\n            {Button.component(\n              {\n                className: 'Button Button--primary',\n                type: 'submit',\n                loading: this.loading,\n              },\n              app.translator.trans('wusong8899-transfer-money.forum.ok')\n            )}\n            &nbsp;\n            {Button.component(\n              {\n                className: 'Button transferMoneyButton--gray',\n                loading: this.loading,\n                onclick: () => {\n                  this.hide();\n\n                  if (typeof (this.attrs as any).callback === 'function') {\n                    (this.attrs as any).callback();\n                  }\n                },\n              },\n              app.translator.trans('wusong8899-transfer-money.forum.cancel')\n            )}\n          </div>\n        </div>\n      </div>\n    );\n  }\n\n  getTotalNeedMoney() {\n    let moneyTransferValue = Number.parseFloat((globalThis as any).$('#moneyTransferInput').val());\n\n    if (Number.isNaN(moneyTransferValue)) {\n      moneyTransferValue = 0;\n    }\n\n    return Object.keys(this.selectedUsers).length * moneyTransferValue;\n  }\n\n  moneyTransferChanged() {\n    const totalNeedMoney = this.getTotalNeedMoney();\n    const totalNeedMoneyText = this.moneyName.replace('[money]', String(totalNeedMoney));\n    (globalThis as any).$('#needMoneyContainer').text(totalNeedMoneyText);\n  }\n\n  onsubmit(e: Event) {\n    e.preventDefault();\n    const userMoney = app.session.user!.attribute('money');\n    const moneyTransferValue = Number.parseFloat((globalThis as any).$('#moneyTransferInput').val());\n    const moneyTransferValueTotal = this.getTotalNeedMoney();\n    const moneyTransferNotesValue = (globalThis as any).$('#moneyTransferNotesInput').val();\n\n    if (moneyTransferValueTotal > userMoney) {\n      app.alerts.show(Alert, { type: 'error' }, app.translator.trans('wusong8899-transfer-money.forum.transfer-error-insufficient-fund'));\n      return;\n    }\n\n    if (Object.keys(this.selectedUsers).length === 0) {\n      app.alerts.show(\n        Alert,\n        { type: 'error' },\n        app.translator.trans('wusong8899-transfer-money.forum.transfer-error-no-target-user-selected')\n      );\n      return;\n    }\n\n    if (moneyTransferValue > 0) {\n      const moneyTransferData = {\n        moneyTransfer: moneyTransferValue,\n        moneyTransferNotes: moneyTransferNotesValue,\n        selectedUsers: JSON.stringify(Object.keys(this.selectedUsers)),\n      };\n\n      this.loading = true;\n\n      app.store\n        .createRecord('transferMoney')\n        .save(moneyTransferData)\n        .then((payload: any) => {\n          app.store.pushPayload(payload);\n          app.modal.show(TransferMoneySuccessModal);\n          this.loading = false;\n\n          if (typeof (this.attrs as any).callback === 'function') {\n            (this.attrs as any).callback();\n          }\n        })\n        .catch(() => {\n          this.loading = false;\n        });\n    }\n  }\n}\n\n", "import app from 'flarum/forum/app';\nimport Notification from 'flarum/forum/components/Notification';\nimport type NotificationModel from 'flarum/common/models/Notification';\n\nexport default class TransferMoneyNotification extends Notification<{ notification: NotificationModel }> {\n  icon(): string {\n    return 'fas fa-money-bill';\n  }\n\n  href(): string {\n    const user = app.session.user;\n    const username = user ? user.username() : '';\n    return app.route('user.transferHistory', { username });\n  }\n\n  content(): any {\n    const user = this.attrs.notification.fromUser();\n    return app.translator.trans(\n      'wusong8899-transfer-money.forum.notifications.user-transfer-money-to-you',\n      {\n        user,\n      }\n    );\n  }\n\n  excerpt(): any {\n    const notification = this.attrs.notification.subject() as any;\n    const transferMoney = notification && notification.attribute ? notification.attribute('transfer_money_value') : undefined;\n    const transferID = notification && notification.attribute ? notification.attribute('id') : undefined;\n    const moneyName = app.forum.attribute('antoinefr-money.moneyname') || '[money]';\n    const costText = moneyName.replace('[money]', transferMoney);\n\n    return app.translator.trans(\n      'wusong8899-transfer-money.forum.notifications.user-transfer-money-details',\n      {\n        cost: costText,\n        id: transferID,\n      }\n    );\n  }\n}\n\n", "import Component from 'flarum/Component';\nimport Link from 'flarum/components/Link';\nimport avatar from 'flarum/helpers/avatar';\nimport username from 'flarum/helpers/username';\n\nexport default class TransferHistoryListItem extends Component<{ transferHistory: any }> {\n  view() {\n    const { transferHistory } = this.attrs;\n    const currentUserID = app.session.user!.id();\n    const fromUserID = transferHistory.attribute('from_user_id');\n    const assignedAt = transferHistory.assignedAt();\n    const fromUser = transferHistory.fromUser();\n    const targetUser = transferHistory.targetUser();\n    const transferMoney = transferHistory.transferMoney();\n    const transferNotes = transferHistory.notes();\n    const transferNotesText = transferNotes ? transferNotes : app.translator.trans('wusong8899-transfer-money.forum.transfer-list-transfer-notes-none');\n    const transferID = transferHistory.id();\n    const transferType = app.translator.trans(currentUserID == fromUserID ? 'wusong8899-transfer-money.forum.transfer-money-out' : 'wusong8899-transfer-money.forum.transfer-money-in');\n    const transferTypeStyle = currentUserID == fromUserID ? 'color:red' : 'color:green';\n\n    const moneyName = app.forum.attribute('antoinefr-money.moneyname') || '[money]';\n    const transferMoneyText = moneyName.replace('[money]', transferMoney);\n\n    return (\n      <div className=\"transferHistoryContainer\">\n        <div style=\"padding-top: 5px;\">\n          <b>{app.translator.trans('wusong8899-transfer-money.forum.transfer-list-type')}: </b>\n          <span style={transferTypeStyle as any}>{transferType}</span>&nbsp;|&nbsp;\n\n          <b>{app.translator.trans('wusong8899-transfer-money.forum.transfer-list-assign-at')}: </b>\n          {assignedAt}\n        </div>\n\n        <div style=\"padding-top: 5px;\">\n          <b>{app.translator.trans('wusong8899-transfer-money.forum.transfer-list-id')}: </b>\n          {transferID}&nbsp;|&nbsp;\n          <b>{app.translator.trans('wusong8899-transfer-money.forum.transfer-list-from-user')}: </b>\n          <Link href={fromUser ? app.route.user(fromUser) : '#'} className=\"transferHistoryUser\" style=\"color:var(--heading-color)\">\n            {avatar(fromUser)} {username(fromUser)}\n          </Link>\n          &nbsp;|&nbsp;\n\n          <b>{app.translator.trans('wusong8899-transfer-money.forum.transfer-list-target-user')}: </b>\n          <Link href={targetUser ? app.route.user(targetUser) : '#'} className=\"transferHistoryUser\" style=\"color:var(--heading-color)\">\n            {avatar(targetUser)} {username(targetUser)}\n          </Link>\n          &nbsp;|&nbsp;\n          <b>{app.translator.trans('wusong8899-transfer-money.forum.transfer-list-transfer-amount')}: </b>\n          {transferMoneyText}&nbsp;\n\n          {transferNotes && (\n            <span>\n              |&nbsp;\n              <b>{app.translator.trans('wusong8899-transfer-money.forum.transfer-list-transfer-notes')}: </b>\n              {transferNotesText}\n            </span>\n          )}\n        </div>\n      </div>\n    );\n  }\n}\n\n", "import Component from 'flarum/Component';\nimport app from 'flarum/app';\nimport LoadingIndicator from 'flarum/components/LoadingIndicator';\nimport Button from 'flarum/components/Button';\nimport type Mithril from 'mithril';\n\nimport TransferHistoryListItem from './TransferHistoryListItem';\n\nexport default class TransferHistoryList extends Component<{ params: { user: any } }> {\n  loading!: boolean;\n  moreResults!: boolean;\n  transferHistory!: any[];\n  user!: any;\n\n  oninit(vnode: Mithril.Vnode<any, this>): void {\n    super.oninit(vnode);\n    this.loading = true;\n    this.moreResults = false;\n    this.transferHistory = [];\n    this.user = this.attrs.params.user;\n    this.loadResults();\n  }\n\n  view() {\n    if (this.loading) {\n      const loading = LoadingIndicator.component({ size: 'large' });\n      return <div>{loading}</div>;\n    }\n\n    return (\n      <div>\n        <div style=\"padding-bottom:10px; font-size: 24px;font-weight: bold;\">\n          {app.translator.trans('wusong8899-transfer-money.forum.transfer-money-history')}\n        </div>\n        <ul style=\"margin: 0;padding: 0;list-style-type: none;position: relative;\">\n          {this.transferHistory.map((transferHistory) => {\n            return (\n              <li style=\"padding-top:5px\" key={transferHistory.id()} data-id={transferHistory.id()}>\n                {TransferHistoryListItem.component({ transferHistory })}\n              </li>\n            );\n          })}\n        </ul>\n\n        {!this.loading && this.transferHistory.length === 0 && (\n          <div>\n            <div style=\"font-size:1.4em;color: var(--muted-more-color);text-align: center;height: 300px;line-height: 100px;\">\n              {app.translator.trans('wusong8899-transfer-money.forum.transfer-list-empty')}\n            </div>\n          </div>\n        )}\n\n        {this.hasMoreResults() && (\n          <div style=\"text-align:center;padding:20px\">\n            <Button className={'Button Button--primary'} disabled={this.loading} loading={this.loading} onclick={() => this.loadMore()}>\n              {app.translator.trans('wusong8899-transfer-money.forum.transfer-list-load-more')}\n            </Button>\n          </div>\n        )}\n      </div>\n    );\n  }\n\n  loadMore() {\n    this.loading = true;\n    this.loadResults(this.transferHistory.length);\n  }\n\n  parseResults(results: any) {\n    this.moreResults = !!results.payload.links && !!results.payload.links.next;\n    [].push.apply(this.transferHistory, results);\n    this.loading = false;\n    m.redraw();\n\n    return results;\n  }\n\n  hasMoreResults() {\n    return this.moreResults;\n  }\n\n  loadResults(offset = 0) {\n    return app.store\n      .find('transferHistory', {\n        filter: {\n          user: this.user.id(),\n        },\n        page: {\n          offset,\n        },\n      })\n      .catch(() => {})\n      .then(this.parseResults.bind(this));\n  }\n}\n\n", "import UserPage from 'flarum/components/UserPage';\nimport TransferHistoryList from './TransferHistoryList';\nimport type Mithril from 'mithril';\n\nexport default class TransferHistoryPage extends UserPage {\n  oninit(vnode: Mithril.Vnode<any, this>): void {\n    super.oninit(vnode);\n    this.loadUser(m.route.param('username'));\n  }\n\n  content(): JSX.Element {\n    return (\n      <div className=\"TransferHistoryPage\">\n        {TransferHistoryList.component({\n          params: {\n            user: this.user,\n          },\n        })}\n      </div>\n    );\n  }\n}\n\n", "import { extend } from 'flarum/common/extend';\nimport LinkButton from 'flarum/components/LinkButton';\nimport UserPage from 'flarum/components/UserPage';\nimport TransferHistoryPage from './components/TransferHistoryPage';\n\nexport default function addTransferMoneyPage(): void {\n  (app as any).routes['user.transferHistory'] = {\n    component: TransferHistoryPage,\n    path: '/u/:username/transferHistory',\n  };\n\n  extend(UserPage.prototype, 'navItems', function addNavItem(items: any) {\n    if (app.session.user) {\n      const currentUserID = app.session.user.id();\n      const targetUserID = this.user.id();\n\n      if (currentUserID === targetUserID) {\n        items.add(\n          'transferMoney',\n          LinkButton.component(\n            {\n              href: app.route('user.transferHistory', {\n                username: this.user.username(),\n              }),\n              icon: 'fas fa-money-bill',\n            },\n            [app.translator.trans('wusong8899-transfer-money.forum.transfer-money-history')]\n          ),\n          10\n        );\n      }\n    }\n  });\n}\n\n", "import { extend } from \"flarum/extend\";\r\nimport SessionDropdown from 'flarum/forum/components/SessionDropdown';\r\nimport TransferMoneyModal from './components/TransferMoneyModal';\r\n\r\nconst checkTime = 10;\r\n\r\nfunction detachTransferMoneyMenu() {\r\n  const moneyTransferClient1Customization = app.forum.attribute('moneyTransferClient1Customization');\r\n\r\n  if (moneyTransferClient1Customization !== '1') {\r\n    return;\r\n  }\r\n\r\n  let transferMoneyLabelContainer = document.getElementById(\"transferMoneyLabelContainer\");\r\n\r\n  if (transferMoneyLabelContainer !== null) {\r\n    $(transferMoneyLabelContainer).remove();\r\n    // $(\"#app-navigation\").css(\"height\",\"var(--header-height-phone)\");\r\n    // $(\"#content .container .IndexPage-results\").css(\"marginTop\",\"15px\");\r\n  }\r\n}\r\n\r\nfunction attachTransferMoneyMenu(vdom: Vnode<any>, _user: User): void {\r\n  const isMobileView = $(\"#drawer\").css('visibility') === \"hidden\";\r\n  const moneyTransferClient1Customization = app.forum.attribute('moneyTransferClient1Customization');\r\n\r\n  if (isMobileView === false) { return; }\r\n  if (moneyTransferClient1Customization !== '1') { return; }\r\n\r\n  $(\"#content .IndexPage-nav .item-nav\").css(\"display\", \"none\");\r\n  $(\"#content .IndexPage-nav .item-newDiscussion\").remove();\r\n\r\n  let task = setInterval(function () {\r\n    if (vdom.dom) {\r\n      clearInterval(task);\r\n\r\n      if (vdom.dom !== undefined) {\r\n        $(\"#content .IndexPage-nav .item-nav\").css(\"display\", \"none\");\r\n        $(\"#content .IndexPage-nav .item-newDiscussion\").remove();\r\n\r\n        let transferMoneyLabelContainer = document.getElementById(\"transferMoneyLabelContainer\");\r\n\r\n        if (transferMoneyLabelContainer !== null) {\r\n          return;\r\n        }\r\n\r\n        $(\"#content .IndexPage-nav .item-nav .ButtonGroup\").removeClass(\"App-titleControl\");\r\n        $(\"#content .IndexPage-nav .item-nav .ButtonGroup button\").addClass(\"Button--link\");\r\n        let itemNav = $(\"#content .IndexPage-nav .item-nav\").clone();\r\n\r\n        if (itemNav.length > 0) {\r\n          $(\"#itemNavClone\").remove();\r\n          $(itemNav).attr('id', \"itemNavClone\");\r\n          $(itemNav).css('display', \"\");\r\n          $(\"#header-secondary .Header-controls\").prepend(itemNav);\r\n        }\r\n\r\n        const appNavigation = document.getElementById(\"app-navigation\");\r\n        const moneyName = app.forum.attribute('antoinefr-money.moneyname') || '[money]';\r\n        const userMoneyText = moneyName.replace('[money]', app.session.user.attribute(\"money\"));\r\n\r\n        transferMoneyLabelContainer = document.createElement(\"div\");\r\n        transferMoneyLabelContainer.id = \"transferMoneyLabelContainer\";\r\n        transferMoneyLabelContainer.className = \"clientCustomizeWithdrawalButtonContainer\";\r\n\r\n        const transferMoneyContainer = document.createElement(\"div\");\r\n        transferMoneyContainer.className = \"clientCustomizeWithdrawalHeaderItems clientCustomizeWithdrawalHeaderTotalMoney\";\r\n\r\n        const transferMoneyText = document.createElement(\"div\");\r\n        transferMoneyText.innerHTML = '<span style=\"font-size:16px;\"><i class=\"fab fa-bitcoin\" style=\"padding-right: 8px;color: gold;\"></i></span>' + userMoneyText;\r\n        transferMoneyText.className = \"clientCustomizeWithdrawalHeaderText\"\r\n\r\n        const transferMoneyIcon = document.createElement(\"div\");\r\n        transferMoneyIcon.innerHTML = '<i class=\"fas fa-wallet\"></i>';\r\n        transferMoneyIcon.className = \"clientCustomizeWithdrawalHeaderIcon\";\r\n\r\n        transferMoneyContainer.appendChild(transferMoneyText);\r\n        transferMoneyContainer.appendChild(transferMoneyIcon);\r\n\r\n        const transferMoneyButtonText = document.createElement(\"div\");\r\n        transferMoneyButtonText.innerHTML = app.translator.trans('wusong8899-transfer-money.forum.withdrawal');\r\n        transferMoneyButtonText.className = \"clientCustomizeWithdrawalHeaderItems clientCustomizeWithdrawalHeaderWithdrawal\";\r\n\r\n        $(transferMoneyButtonText).click(function () {\r\n          app.modal.show(TransferMoneyModal);\r\n        });\r\n\r\n        const userAvatarContainer = document.createElement(\"div\");\r\n        userAvatarContainer.className = \"clientCustomizeWithdrawalHeaderItems clientCustomizeWithdrawalHeaderUser\";\r\n\r\n        const avatarClone = $(\"#header-secondary .item-session .SessionDropdown\").clone();\r\n        $(avatarClone).attr('id', \"avatarClone\");\r\n        $(avatarClone).addClass(\"App-primaryControl\");\r\n\r\n        $(userAvatarContainer).html(avatarClone);\r\n\r\n        let hideNavToggle = \"\";\r\n        $(avatarClone).on('click', function () {\r\n          hideNavToggle = hideNavToggle === \"\" ? \"none\" : \"\";\r\n          $(\"#content .IndexPage-nav\").css(\"display\", hideNavToggle);\r\n        });\r\n\r\n        transferMoneyLabelContainer.appendChild(transferMoneyContainer);\r\n        transferMoneyLabelContainer.appendChild(transferMoneyButtonText);\r\n        transferMoneyLabelContainer.appendChild(userAvatarContainer);\r\n        appNavigation.appendChild(transferMoneyLabelContainer);\r\n      }\r\n    }\r\n  }, checkTime);\r\n}\r\n\r\nexport default function () {\r\n  extend(SessionDropdown.prototype, 'view', function (vnode) {\r\n    if (!app.session.user) {\r\n      return;\r\n    }\r\n\r\n    const routeName = app.current.get('routeName');\r\n\r\n    if (routeName) {\r\n      if (routeName !== \"tags\") {\r\n        detachTransferMoneyMenu();\r\n      } else {\r\n        attachTransferMoneyMenu(vnode, this.attrs.user);\r\n      }\r\n    }\r\n  });\r\n}\r\n", "import { extend } from 'flarum/extend';\r\nimport UserControls from 'flarum/utils/UserControls';\r\nimport NotificationGrid from \"flarum/components/NotificationGrid\";\r\nimport SessionDropdown from 'flarum/forum/components/SessionDropdown';\r\nimport Button from 'flarum/components/Button';\r\n\r\nimport TransferMoney from \"./model/TransferMoney\";\r\nimport TransferMoneyModal from './components/TransferMoneyModal';\r\nimport TransferMoneyNotification from \"./components/TransferMoneyNotification\";\r\nimport addTransferMoneyPage from \"./addTransferMoneyPage\";\r\nimport addClient1CustomizationFeatures from \"./addClient1CustomizationFeatures\";\r\n\r\n\r\napp.initializers.add('wusong8899-money-transfer', () => {\r\n  app.store.models.transferMoney = TransferMoney;\r\n  app.notificationComponents.transferMoney = TransferMoneyNotification;\r\n\r\n  addTransferMoneyPage();\r\n  addClient1CustomizationFeatures();\r\n\r\n  extend(NotificationGrid.prototype, \"notificationTypes\", function (items) {\r\n    items.add(\"transferMoney\", {\r\n      name: \"transferMoney\",\r\n      icon: \"fas fa-dollar-sign\",\r\n      label: app.translator.trans(\r\n        \"wusong8899-transfer-money.forum.receive-transfer-from-user\"\r\n      ),\r\n    });\r\n  });\r\n\r\n  extend(UserControls, 'moderationControls', (items, user) => {\r\n    const allowUseTranferMoney = app.forum.attribute('allowUseTranferMoney');\r\n\r\n    if(app.session.user && allowUseTranferMoney){\r\n      const currentUserID = app.session.user.id();\r\n      const targetUserID = user.id();\r\n      \r\n      if(currentUserID!==targetUserID){\r\n        items.add('transferMoney', Button.component({\r\n            icon: 'fas fa-money-bill',\r\n            onclick: () => app.modal.show(TransferMoneyModal, {user})\r\n          }, app.translator.trans('wusong8899-transfer-money.forum.transfer-money'))\r\n        );\r\n      }\r\n    }\r\n  });\r\n\r\n  extend(SessionDropdown.prototype, 'items', function (items) {\r\n    if (!app.session.user) {\r\n      return;\r\n    }\r\n\r\n    items.add(\r\n      'transferMoney',\r\n      Button.component(\r\n        {\r\n          icon: 'fas fa-money-bill',\r\n          onclick: () => {\r\n            app.modal.show(TransferMoneyModal)\r\n          },\r\n        },\r\n        app.translator.trans('wusong8899-transfer-money.forum.transfer-money')\r\n      ),\r\n      -1\r\n    );\r\n  });\r\n});\r\n"], "names": ["TransferMoney", "Model", "UserSearchSource", "query", "results", "user", "avatar", "username", "highlight", "TransferMoneySearchModal", "Search", "vnode", "$search", "target", "e", "loading", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "app", "recipient", "userName", "userAvatar", "userID", "classList", "extractText", "source", "LoadingIndicator", "items", "ItemList", "value", "values", "type", "id", "store", "_TransferMoneySuccessModal", "Modal", "<PERSON><PERSON>", "TransferMoneySuccessModal", "_TransferMoneyModal", "Stream", "targetUser", "SearchState", "moneyTransferValue", "totalNeedMoney", "totalNeedMoneyText", "userMoney", "moneyTransferValueTotal", "moneyTransferNotesValue", "<PERSON><PERSON>", "moneyTransferData", "payload", "TransferMoneyModal", "TransferMoneyNotification", "Notification", "notification", "transferMoney", "transferID", "costText", "TransferHistoryListItem", "Component", "transferHistory", "currentUserID", "fromUserID", "assignedAt", "fromUser", "transferNotes", "transferNotesText", "transferType", "transferTypeStyle", "transferMoneyText", "Link", "TransferHistoryList", "offset", "TransferHistoryPage", "UserPage", "addTransferMoneyPage", "extend", "targetUserID", "LinkButton", "checkTime", "detachTransferMoneyMenu", "transferMoneyLabelContainer", "attachTransferMoneyMenu", "vdom", "_user", "isMobile<PERSON>iew", "moneyTransferClient1Customization", "task", "itemNav", "appNavigation", "userMoneyText", "transferMoneyContainer", "transferMoneyIcon", "transferMoneyButtonText", "userAvatarContainer", "avatar<PERSON><PERSON>", "hideNavToggle", "addClient1CustomizationFeatures", "SessionDropdown", "routeName", "NotificationGrid", "UserControls", "allowUseTranferMoney"], "mappings": "oFAGA,MAAqBA,UAAsBC,CAAM,CAAC,CAGlD,OAAO,OAAOD,EAAc,UAAW,CAErC,GAAIC,EAAM,UAA8B,IAAI,EAC5C,cAAeA,EAAM,UAAkB,sBAAsB,EAC7D,MAAOA,EAAM,UAAyB,OAAO,EAC7C,WAAYA,EAAM,UAAkB,aAAa,EACjD,SAAUA,EAAM,OAAO,UAAU,EACjC,WAAYA,EAAM,OAAO,YAAY,CACvC,CAAC,ECTD,MAAqBC,EAAiB,CAAtC,aAAA,CACE,KAAQ,QAAiB,CAAA,CAAC,CAE1B,MAAM,OAAOC,EAA8B,CACzC,GAAI,CAACA,GAASA,EAAM,OAAS,EAAG,CAC9B,KAAK,QAAU,CAAA,EACf,MACF,CAEA,MAAMC,EAAU,MAAM,IAAI,MAAM,KAAK,QAAS,CAAE,OAAQ,CAAE,EAAGD,GAAS,KAAM,CAAE,MAAO,CAAA,EAAK,EAC1F,KAAK,QAAUC,CACjB,CAEA,KAAKD,EAAqC,CACxC,MAAMC,EAAU,KAAK,QAErB,MAAI,CAACA,GAAW,CAACA,EAAQ,OAAe,CAAA,EAEjCA,EAAQ,IAAKC,GAClB,EAAC,MAAG,UAAU,eAAe,aAAY,SAASA,EAAK,IAAI,IACzD,EAAC,IAAA,CAAE,UAAU,eAAe,SAAS,IAAA,EAClCC,EAAOD,CAAI,EAAE,IAAEE,EAASF,CAAI,IAC5B,OAAA,CAAK,UAAU,sBAAA,EAAwBG,EAAUH,EAAK,WAAYF,CAAK,CAAE,CAC5E,CACF,CACD,CACH,CACF,CCrBA,MAAqBM,WAAiCC,CAAY,CAAlE,aAAA,CAAA,MAAA,GAAA,SAAA,EAGE,KAAA,SAAW,EAAA,CAEX,OAAOC,EAAuC,CAC5C,MAAM,OAAOA,CAAK,EAClB,KAAK,UAAY,KAAK,OAAA,EAAS,SAAS,EAAE,EAAE,UAAU,CAAC,CACzD,CAEA,SAASA,EAA0C,CACjD,MAAM,SAASA,CAAK,EAEpB,MAAMC,EAAU,KAEhB,KAAK,EAAE,iBAAiB,EAAE,GAAG,QAAS,IAAM,CAC1C,MAAMC,EAAS,KAAK,EAAE,sBAAsB,EAE5CD,EAAQ,aAAaC,EAAO,KAAK,OAAO,CAAC,EACzCD,EAAQ,EAAE,kBAAkB,EAAE,MAAA,CAChC,CAAC,EAED,KAAK,EAAE,iBAAiB,EAAE,GAAG,aAAeE,GAAW,CACrD,MAAMD,EAAS,KAAK,EAAEC,EAAE,OAAO,UAAU,EAEzCF,EAAQ,aAAaC,EAAO,KAAK,OAAO,CAAC,EACzCD,EAAQ,EAAE,kBAAkB,EAAE,MAAA,CAChC,CAAC,EAEA,OAAe,EAAE,kBAAkB,EACjC,GAAG,QAAS,IAAM,CACjB,aAAa,KAAK,WAAW,EAC7B,KAAK,SAAW,GAChB,KAAK,YAAc,OAAO,WAAW,IAAM,CACzC,KAAK,SAAW,GAChB,EAAE,OAAA,CACJ,EAAG,GAAG,CACR,CAAC,EACA,GAAG,UAAW,IAAM,CACnB,aAAa,KAAK,WAAW,CAC/B,CAAC,EAEH,MAAM,SAASD,CAAK,CACtB,CAEA,MAAO,CACD,OAAO,KAAK,YAAY,SAAA,EAAe,KACzC,KAAK,YAAY,SAAS,EAAE,EAG9B,MAAMI,EAAU,KAAK,YAAY,SAAA,GAAc,KAAK,YAAY,WAAW,QAAU,EAEhF,KAAK,UACR,KAAK,QAAU,KAAK,YAAA,EAAc,QAAA,GAGpC,MAAMC,EAAoB,KAAK,MAAM,SAAA,EAAW,QAAA,EAEhD,OACE,EAAC,OAAI,KAAK,SAAS,UAAU,UAC3B,EAAC,OAAI,UAAU,2CAA2C,YAAU,QAAA,EAClE,EAAC,OAAI,MAAM,kFACRC,EAAI,WAAW,MAAM,wDAAwD,CAChF,EAECD,EAAkB,SAAW,KAC3B,MAAA,CAAI,MAAM,0CAA0C,MAAM,+BACxDC,EAAI,WAAW,MAAM,iEAAiE,CACzF,EAGD,KAAK,MACH,SAAA,EACA,QAAA,EACA,IAAKC,GAAmB,CACvB,MAAMC,EAAWZ,EAASW,CAAS,EAC7BE,EAAad,EAAOY,CAAS,EAC7BG,EAASH,EAAU,KAAK,GAC9B,YAAK,MAAM,cAAcG,CAAM,EAAI,IAGhC,MAAA,CAAI,MAAM,8BAA8B,QAAUP,GAAkB,KAAK,gBAAgBI,EAAWJ,CAAC,CAAA,IACnG,OAAA,CAAK,MAAM,sBAAsBM,CAAW,EAAO,IAAED,CACxD,CAEJ,CAAC,CACL,EAEA,EAAC,OAAI,UAAU,YAAA,EACb,EAAC,QAAA,CAAM,QAAS,oCAAoC,KAAK,SAAS,EAAA,EAC/DF,EAAI,WAAW,MAAM,4DAA4D,CACpF,EAEA,EAAC,MAAA,CAAI,UAAU,2CAAA,EACb,EAAC,QAAA,CACC,GAAI,oCAAoC,KAAK,SAAS,GACtD,UAAWK,EAAU,kBAAmB,cAAe,CACrD,KAAM,CAAC,CAAC,KAAK,YAAY,SAAA,EACzB,QAAS,CAAC,CAAC,KAAK,YAAY,SAAA,EAC5B,OAAQ,CAAC,CAAC,KAAK,YAAY,SAAA,EAC3B,QAAS,CAAC,CAAC,KAAK,cAAA,CACjB,EACD,KAAK,SACL,YAAaC,EACXN,EAAI,WAAW,MAAM,wEAAwE,CAAA,EAE/F,MAAO,KAAK,YAAY,SAAA,EACxB,QAAUH,GAAW,KAAK,YAAY,SAASA,EAAE,OAAO,KAAK,EAC7D,QAAS,IAAO,KAAK,SAAW,GAChC,OAAQ,IAAO,KAAK,SAAW,EAAA,CAAA,EAEjC,EAAC,KAAA,CAAG,UAAWQ,EAAU,gBAAiB,iBAAkB,OAAQ,CAAE,GAAI,CAAC,CAACP,EAAS,CAAA,EACjF,KAAK,SAEF,KAAK,QAAgB,IAAKS,GAAgBA,EAAO,KAAK,KAAK,YAAY,UAAU,CAAC,EADnFC,EAAiB,UAAU,CAAE,KAAM,OAAQ,UAAW,kCAAA,CAAoC,CAEhG,CACF,CACF,CACF,CAEJ,CAEA,aAAc,CACZ,MAAMC,EAAQ,IAAIC,EAClB,OAAAD,EAAM,IAAI,QAAS,IAAIxB,EAAkB,EAClCwB,CACT,CAEA,aAAaE,EAAe,CAC1B,MAAMC,EAASD,EAAM,MAAM,GAAG,EACxBE,EAAOD,EAAO,CAAC,EACfE,EAAKF,EAAO,CAAC,EACbX,EAAY,KAAK,cAAcY,EAAMC,CAAE,EACvCV,EAASH,EAAU,KAAK,GAE9B,KAAK,MAAM,SAAA,EAAW,IAAIU,EAAOV,CAAS,EAC1C,KAAK,MAAM,cAAcG,CAAM,EAAI,EACnC,KAAK,YAAY,MAAA,EACjB,KAAK,MAAM,UAAU,KAAK,aAAA,CAAc,EACxC,KAAK,MAAM,SAAA,CACb,CAEA,gBAAgBH,EAAgBJ,EAAU,CACxCA,EAAE,eAAA,EAEF,MAAMO,EAASH,EAAU,KAAK,GAC9B,OAAO,KAAK,MAAM,cAAcG,CAAM,EAGtC,KAAK,MAAM,WAAW,OADT,QACuB,IAAMH,EAAU,IAAI,EACxD,KAAK,MAAM,UAAU,KAAK,aAAA,CAAc,EACxC,KAAK,MAAM,SAAA,CACb,CAEA,cAAe,CAEb,OAD4B,OAAe,EAAE,qBAAqB,EAAE,IAAA,EACxC,OAAO,KAAK,KAAK,MAAM,aAAa,EAAE,MACpE,CAEA,cAAcc,EAAeD,EAAY,CACvC,OAAOd,EAAI,MAAM,QAAQe,EAAOD,CAAE,CACpC,CACF,CCxKA,MAAqBE,EAArB,MAAqBA,UAAkCC,CAAM,CAG3D,OAAOvB,EAAuC,CAC5C,MAAM,OAAOA,CAAK,CACpB,CAEA,WAAoB,CAClB,MAAO,cACT,CAEA,OAAQ,CACN,OAAOM,EAAI,WAAW,MAAM,wDAAwD,CACtF,CAEA,SAAU,CACR,MAAO,CACL,EAAC,OAAI,UAAU,YAAA,IACZ,MAAA,CAAI,MAAM,qBACRkB,EAAO,UACN,CACE,MAAO,aACP,UAAW,yBACX,QAAS,IAAM,CACb,SAAS,OAAA,CACX,CAAA,EAEFlB,EAAI,WAAW,MAAM,oCAAoC,CAAA,CAE7D,CACF,CAAA,CAEJ,CACF,EAhCEgB,EAAO,cAAgB,GADzB,IAAqBG,EAArBH,ECOA,MAAqBI,EAArB,MAAqBA,UAA2BH,CAAM,CASpD,OAAOvB,EAAuC,CAC5C,MAAM,OAAOA,CAAK,EAClB,KAAK,SAAW2B,EAAO,IAAIX,CAAU,EACrC,KAAK,cAAgB,CAAA,EACrB,KAAK,UAAYV,EAAI,MAAM,UAAU,2BAA2B,GAAK,UAErE,MAAMsB,EAAc,KAAK,MAAc,KACnCA,IACF,KAAK,WAAW,IAAI,SAAWA,EAAW,GAAA,EAAMA,CAAU,EAC1D,KAAK,cAAcA,EAAW,GAAA,CAAI,EAAI,GAGxC,KAAK,gBAAkB,IAAIC,EAC3B,KAAK,UAAYF,EAAO,CAAC,CAC3B,CAEA,WAAoB,CAClB,MAAO,cACT,CAEA,OAAQ,CACN,OAAOrB,EAAI,WAAW,MAAM,gDAAgD,CAC9E,CAEA,SAAU,CACR,OACE,EAAC,MAAA,CAAI,UAAU,YAAA,IACZ,MAAA,CAAI,UAAU,MAAA,EACb,EAAC,OAAI,MAAM,uBAAuB,UAAU,yBAAA,EACzCR,GAAyB,UAAU,CAClC,MAAO,KAAK,gBACZ,SAAU,KAAK,SACf,cAAe,KAAK,cACpB,UAAW,KAAK,UAChB,SAAU,UAAY,CACpB,EAAE,OAAA,CACJ,CAAA,CACD,CACH,EAEA,EAAC,MAAA,CAAI,UAAU,cACb,EAAC,QAAA,KACEQ,EAAI,WAAW,MAAM,sDAAsD,EAC3E,KAAK,UAAU,QAAQ,UAAWA,EAAI,QAAQ,KAAM,UAAU,OAAO,CAAC,CACzE,EACA,EAAC,QAAA,CACC,GAAG,qBACH,YAAaA,EAAI,WAAW,MAAM,kEAAkE,EACpG,SAAQ,GACR,UAAU,cACV,KAAK,SACL,KAAK,MACL,IAAI,IACJ,QAAS,IAAM,KAAK,qBAAA,CAAqB,CAAA,EAE3C,EAAC,MAAA,CAAI,MAAM,kBAAA,EACRA,EAAI,WAAW,MAAM,mDAAmD,EACzE,EAAC,OAAA,CAAK,GAAG,oBAAA,EAAsB,KAAK,UAAU,QAAQ,UAAW,OAAO,KAAK,UAAA,CAAW,CAAC,CAAE,CAC7F,CACF,IAEC,MAAA,CAAI,UAAU,YAAA,EACb,EAAC,QAAA,KAAOA,EAAI,WAAW,MAAM,sDAAsD,CAAE,EACrF,EAAC,WAAA,CAAS,GAAG,0BAA0B,UAAU,MAAM,UAAU,cAAc,CACjF,EAEA,EAAC,MAAA,CAAI,UAAU,aAAa,MAAM,qBAAA,EAC/BkB,EAAO,UACN,CACE,UAAW,yBACX,KAAM,SACN,QAAS,KAAK,OAAA,EAEhBlB,EAAI,WAAW,MAAM,oCAAoC,CAAA,EACzD,IAEDkB,EAAO,UACN,CACE,UAAW,mCACX,QAAS,KAAK,QACd,QAAS,IAAM,CACb,KAAK,KAAA,EAED,OAAQ,KAAK,MAAc,UAAa,YACzC,KAAK,MAAc,SAAA,CAExB,CAAA,EAEFlB,EAAI,WAAW,MAAM,wCAAwC,CAAA,CAEjE,CACF,CACF,CAEJ,CAEA,mBAAoB,CAClB,IAAIwB,EAAqB,OAAO,WAAY,WAAmB,EAAE,qBAAqB,EAAE,KAAK,EAE7F,OAAI,OAAO,MAAMA,CAAkB,IACjCA,EAAqB,GAGhB,OAAO,KAAK,KAAK,aAAa,EAAE,OAASA,CAClD,CAEA,sBAAuB,CACrB,MAAMC,EAAiB,KAAK,kBAAA,EACtBC,EAAqB,KAAK,UAAU,QAAQ,UAAW,OAAOD,CAAc,CAAC,EAClF,WAAmB,EAAE,qBAAqB,EAAE,KAAKC,CAAkB,CACtE,CAEA,SAAS,EAAU,CACjB,EAAE,eAAA,EACF,MAAMC,EAAY3B,EAAI,QAAQ,KAAM,UAAU,OAAO,EAC/CwB,EAAqB,OAAO,WAAY,WAAmB,EAAE,qBAAqB,EAAE,KAAK,EACzFI,EAA0B,KAAK,kBAAA,EAC/BC,EAA2B,WAAmB,EAAE,0BAA0B,EAAE,IAAA,EAElF,GAAID,EAA0BD,EAAW,CACvC3B,EAAI,OAAO,KAAK8B,EAAO,CAAE,KAAM,OAAA,EAAW9B,EAAI,WAAW,MAAM,kEAAkE,CAAC,EAClI,MACF,CAEA,GAAI,OAAO,KAAK,KAAK,aAAa,EAAE,SAAW,EAAG,CAChDA,EAAI,OAAO,KACT8B,EACA,CAAE,KAAM,OAAA,EACR9B,EAAI,WAAW,MAAM,wEAAwE,CAAA,EAE/F,MACF,CAEA,GAAIwB,EAAqB,EAAG,CAC1B,MAAMO,EAAoB,CACxB,cAAeP,EACf,mBAAoBK,EACpB,cAAe,KAAK,UAAU,OAAO,KAAK,KAAK,aAAa,CAAC,CAAA,EAG/D,KAAK,QAAU,GAEf7B,EAAI,MACD,aAAa,eAAe,EAC5B,KAAK+B,CAAiB,EACtB,KAAMC,GAAiB,CACtBhC,EAAI,MAAM,YAAYgC,CAAO,EAC7BhC,EAAI,MAAM,KAAKmB,CAAyB,EACxC,KAAK,QAAU,GAEX,OAAQ,KAAK,MAAc,UAAa,YACzC,KAAK,MAAc,SAAA,CAExB,CAAC,EACA,MAAM,IAAM,CACX,KAAK,QAAU,EACjB,CAAC,CACL,CACF,CACF,EAvKEC,EAAO,cAAgB,GADzB,IAAqBa,EAArBb,ECRA,MAAqBc,WAAkCC,CAAkD,CACvG,MAAe,CACb,MAAO,mBACT,CAEA,MAAe,CACb,MAAM/C,EAAOY,EAAI,QAAQ,KACnBV,EAAWF,EAAOA,EAAK,SAAA,EAAa,GAC1C,OAAOY,EAAI,MAAM,uBAAwB,CAAE,SAAAV,EAAU,CACvD,CAEA,SAAe,CACb,MAAMF,EAAO,KAAK,MAAM,aAAa,SAAA,EACrC,OAAOY,EAAI,WAAW,MACpB,2EACA,CACE,KAAAZ,CAAA,CACF,CAEJ,CAEA,SAAe,CACb,MAAMgD,EAAe,KAAK,MAAM,aAAa,QAAA,EACvCC,EAAgBD,GAAgBA,EAAa,UAAYA,EAAa,UAAU,sBAAsB,EAAI,OAC1GE,EAAaF,GAAgBA,EAAa,UAAYA,EAAa,UAAU,IAAI,EAAI,OAErFG,GADYvC,EAAI,MAAM,UAAU,2BAA2B,GAAK,WAC3C,QAAQ,UAAWqC,CAAa,EAE3D,OAAOrC,EAAI,WAAW,MACpB,4EACA,CACE,KAAMuC,EACN,GAAID,CAAA,CACN,CAEJ,CACF,CCnCA,MAAqBE,WAAgCC,CAAoC,CACvF,MAAO,CACL,KAAM,CAAE,gBAAAC,GAAoB,KAAK,MAC3BC,EAAgB,IAAI,QAAQ,KAAM,GAAA,EAClCC,EAAaF,EAAgB,UAAU,cAAc,EACrDG,EAAaH,EAAgB,WAAA,EAC7BI,EAAWJ,EAAgB,SAAA,EAC3BpB,EAAaoB,EAAgB,WAAA,EAC7BL,EAAgBK,EAAgB,cAAA,EAChCK,EAAgBL,EAAgB,MAAA,EAChCM,EAAoBD,GAAgC,IAAI,WAAW,MAAM,mEAAmE,EAC5IT,EAAaI,EAAgB,GAAA,EAC7BO,EAAe,IAAI,WAAW,MAAMN,GAAiBC,EAAa,qDAAuD,mDAAmD,EAC5KM,EAAoBP,GAAiBC,EAAa,YAAc,cAGhEO,GADY,IAAI,MAAM,UAAU,2BAA2B,GAAK,WAClC,QAAQ,UAAWd,CAAa,EAEpE,OACE,EAAC,MAAA,CAAI,UAAU,0BAAA,IACZ,MAAA,CAAI,MAAM,mBAAA,EACT,EAAC,IAAA,KAAG,IAAI,WAAW,MAAM,oDAAoD,EAAE,IAAE,EACjF,EAAC,OAAA,CAAK,MAAOa,CAAA,EAA2BD,CAAa,EAAO,MAE5D,EAAC,IAAA,KAAG,IAAI,WAAW,MAAM,yDAAyD,EAAE,IAAE,EACrFJ,CACH,EAEA,EAAC,MAAA,CAAI,MAAM,mBAAA,EACT,EAAC,SAAG,IAAI,WAAW,MAAM,kDAAkD,EAAE,IAAE,EAC9EP,EAAW,MACZ,EAAC,IAAA,KAAG,IAAI,WAAW,MAAM,yDAAyD,EAAE,IAAE,EACtF,EAACc,EAAA,CAAK,KAAMN,EAAW,IAAI,MAAM,KAAKA,CAAQ,EAAI,IAAK,UAAU,sBAAsB,MAAM,4BAAA,EAC1FzD,EAAOyD,CAAQ,EAAE,IAAExD,EAASwD,CAAQ,CACvC,EAAO,MAGP,EAAC,IAAA,KAAG,IAAI,WAAW,MAAM,2DAA2D,EAAE,IAAE,EACxF,EAACM,EAAA,CAAK,KAAM9B,EAAa,IAAI,MAAM,KAAKA,CAAU,EAAI,IAAK,UAAU,sBAAsB,MAAM,4BAAA,EAC9FjC,EAAOiC,CAAU,EAAE,IAAEhC,EAASgC,CAAU,CAC3C,EAAO,MAEP,EAAC,IAAA,KAAG,IAAI,WAAW,MAAM,+DAA+D,EAAE,IAAE,EAC3F6B,EAAkB,IAElBJ,GACC,EAAC,OAAA,KAAK,KAEJ,EAAC,SAAG,IAAI,WAAW,MAAM,8DAA8D,EAAE,IAAE,EAC1FC,CACH,CAEJ,CACF,CAEJ,CACF,CCrDA,MAAqBK,WAA4BZ,CAAqC,CAMpF,OAAO/C,EAAuC,CAC5C,MAAM,OAAOA,CAAK,EAClB,KAAK,QAAU,GACf,KAAK,YAAc,GACnB,KAAK,gBAAkB,CAAA,EACvB,KAAK,KAAO,KAAK,MAAM,OAAO,KAC9B,KAAK,YAAA,CACP,CAEA,MAAO,CACL,GAAI,KAAK,QAAS,CAChB,MAAMI,EAAUU,EAAiB,UAAU,CAAE,KAAM,QAAS,EAC5D,OAAO,EAAC,WAAKV,CAAQ,CACvB,CAEA,OACE,EAAC,WACC,EAAC,MAAA,CAAI,MAAM,yDAAA,EACRE,EAAI,WAAW,MAAM,wDAAwD,CAChF,EACA,EAAC,MAAG,MAAM,gEAAA,EACP,KAAK,gBAAgB,IAAK0C,KAEtB,KAAA,CAAG,MAAM,kBAAkB,IAAKA,EAAgB,GAAA,EAAM,UAASA,EAAgB,IAAG,EAChFF,GAAwB,UAAU,CAAE,gBAAAE,CAAA,CAAiB,CACxD,CAEH,CACH,EAEC,CAAC,KAAK,SAAW,KAAK,gBAAgB,SAAW,GAChD,EAAC,WACC,EAAC,MAAA,CAAI,MAAM,qGAAA,EACR1C,EAAI,WAAW,MAAM,qDAAqD,CAC7E,CACF,EAGD,KAAK,eAAA,GACJ,EAAC,MAAA,CAAI,MAAM,gCAAA,EACT,EAACkB,EAAA,CAAO,UAAW,yBAA0B,SAAU,KAAK,QAAS,QAAS,KAAK,QAAS,QAAS,IAAM,KAAK,SAAA,CAAS,EACtHlB,EAAI,WAAW,MAAM,yDAAyD,CACjF,CACF,CAEJ,CAEJ,CAEA,UAAW,CACT,KAAK,QAAU,GACf,KAAK,YAAY,KAAK,gBAAgB,MAAM,CAC9C,CAEA,aAAab,EAAc,CACzB,YAAK,YAAc,CAAC,CAACA,EAAQ,QAAQ,OAAS,CAAC,CAACA,EAAQ,QAAQ,MAAM,KACtE,CAAA,EAAG,KAAK,MAAM,KAAK,gBAAiBA,CAAO,EAC3C,KAAK,QAAU,GACf,EAAE,OAAA,EAEKA,CACT,CAEA,gBAAiB,CACf,OAAO,KAAK,WACd,CAEA,YAAYmE,EAAS,EAAG,CACtB,OAAOtD,EAAI,MACR,KAAK,kBAAmB,CACvB,OAAQ,CACN,KAAM,KAAK,KAAK,GAAA,CAAG,EAErB,KAAM,CACJ,OAAAsD,CAAA,CACF,CACD,EACA,MAAM,IAAM,CAAC,CAAC,EACd,KAAK,KAAK,aAAa,KAAK,IAAI,CAAC,CACtC,CACF,CC1FA,MAAqBC,WAA4BC,CAAS,CACxD,OAAO9D,EAAuC,CAC5C,MAAM,OAAOA,CAAK,EAClB,KAAK,SAAS,EAAE,MAAM,MAAM,UAAU,CAAC,CACzC,CAEA,SAAuB,CACrB,OACE,EAAC,MAAA,CAAI,UAAU,qBAAA,EACZ2D,GAAoB,UAAU,CAC7B,OAAQ,CACN,KAAM,KAAK,IAAA,CACb,CACD,CACH,CAEJ,CACF,CChBA,SAAwBI,IAA6B,CAClD,IAAY,OAAO,sBAAsB,EAAI,CAC5C,UAAWF,GACX,KAAM,8BAAA,EAGRG,EAAAA,OAAOF,EAAS,UAAW,WAAY,SAAoB/C,EAAY,CACrE,GAAI,IAAI,QAAQ,KAAM,CACpB,MAAMkC,EAAgB,IAAI,QAAQ,KAAK,GAAA,EACjCgB,EAAe,KAAK,KAAK,GAAA,EAE3BhB,IAAkBgB,GACpBlD,EAAM,IACJ,gBACAmD,EAAW,UACT,CACE,KAAM,IAAI,MAAM,uBAAwB,CACtC,SAAU,KAAK,KAAK,SAAA,CAAS,CAC9B,EACD,KAAM,mBAAA,EAER,CAAC,IAAI,WAAW,MAAM,wDAAwD,CAAC,CAAA,EAEjF,EAAA,CAGN,CACF,CAAC,CACH,CC7BA,MAAMC,GAAY,GAElB,SAASC,IAA0B,CAGjC,GAF0C,IAAI,MAAM,UAAU,mCAAmC,IAEvD,IACxC,OAGF,IAAIC,EAA8B,SAAS,eAAe,6BAA6B,EAEnFA,IAAgC,MAClC,EAAEA,CAA2B,EAAE,OAAA,CAInC,CAEA,SAASC,GAAwBC,EAAkBC,EAAmB,CACpE,MAAMC,EAAe,EAAE,SAAS,EAAE,IAAI,YAAY,IAAM,SAClDC,EAAoC,IAAI,MAAM,UAAU,mCAAmC,EAGjG,GADID,IAAiB,IACjBC,IAAsC,IAAO,OAEjD,EAAE,mCAAmC,EAAE,IAAI,UAAW,MAAM,EAC5D,EAAE,6CAA6C,EAAE,OAAA,EAEjD,IAAIC,EAAO,YAAY,UAAY,CACjC,GAAIJ,EAAK,MACP,cAAcI,CAAI,EAEdJ,EAAK,MAAQ,QAAW,CAC1B,EAAE,mCAAmC,EAAE,IAAI,UAAW,MAAM,EAC5D,EAAE,6CAA6C,EAAE,OAAA,EAEjD,IAAIF,EAA8B,SAAS,eAAe,6BAA6B,EAEvF,GAAIA,IAAgC,KAClC,OAGF,EAAE,gDAAgD,EAAE,YAAY,kBAAkB,EAClF,EAAE,uDAAuD,EAAE,SAAS,cAAc,EAClF,IAAIO,EAAU,EAAE,mCAAmC,EAAE,MAAA,EAEjDA,EAAQ,OAAS,IACnB,EAAE,eAAe,EAAE,OAAA,EACnB,EAAEA,CAAO,EAAE,KAAK,KAAM,cAAc,EACpC,EAAEA,CAAO,EAAE,IAAI,UAAW,EAAE,EAC5B,EAAE,oCAAoC,EAAE,QAAQA,CAAO,GAGzD,MAAMC,EAAgB,SAAS,eAAe,gBAAgB,EAExDC,GADY,IAAI,MAAM,UAAU,2BAA2B,GAAK,WACtC,QAAQ,UAAW,IAAI,QAAQ,KAAK,UAAU,OAAO,CAAC,EAEtFT,EAA8B,SAAS,cAAc,KAAK,EAC1DA,EAA4B,GAAK,8BACjCA,EAA4B,UAAY,2CAExC,MAAMU,EAAyB,SAAS,cAAc,KAAK,EAC3DA,EAAuB,UAAY,iFAEnC,MAAMtB,EAAoB,SAAS,cAAc,KAAK,EACtDA,EAAkB,UAAY,8GAAgHqB,EAC9IrB,EAAkB,UAAY,sCAE9B,MAAMuB,EAAoB,SAAS,cAAc,KAAK,EACtDA,EAAkB,UAAY,gCAC9BA,EAAkB,UAAY,sCAE9BD,EAAuB,YAAYtB,CAAiB,EACpDsB,EAAuB,YAAYC,CAAiB,EAEpD,MAAMC,EAA0B,SAAS,cAAc,KAAK,EAC5DA,EAAwB,UAAY,IAAI,WAAW,MAAM,4CAA4C,EACrGA,EAAwB,UAAY,iFAEpC,EAAEA,CAAuB,EAAE,MAAM,UAAY,CAC3C,IAAI,MAAM,KAAK1C,CAAkB,CACnC,CAAC,EAED,MAAM2C,EAAsB,SAAS,cAAc,KAAK,EACxDA,EAAoB,UAAY,2EAEhC,MAAMC,EAAc,EAAE,kDAAkD,EAAE,MAAA,EAC1E,EAAEA,CAAW,EAAE,KAAK,KAAM,aAAa,EACvC,EAAEA,CAAW,EAAE,SAAS,oBAAoB,EAE5C,EAAED,CAAmB,EAAE,KAAKC,CAAW,EAEvC,IAAIC,EAAgB,GACpB,EAAED,CAAW,EAAE,GAAG,QAAS,UAAY,CACrCC,EAAgBA,IAAkB,GAAK,OAAS,GAChD,EAAE,yBAAyB,EAAE,IAAI,UAAWA,CAAa,CAC3D,CAAC,EAEDf,EAA4B,YAAYU,CAAsB,EAC9DV,EAA4B,YAAYY,CAAuB,EAC/DZ,EAA4B,YAAYa,CAAmB,EAC3DL,EAAc,YAAYR,CAA2B,CACvD,CAEJ,EAAGF,EAAS,CACd,CAEA,SAAAkB,IAA2B,CACzBrB,EAAAA,OAAOsB,EAAgB,UAAW,OAAQ,SAAUtF,EAAO,CACzD,GAAI,CAAC,IAAI,QAAQ,KACf,OAGF,MAAMuF,EAAY,IAAI,QAAQ,IAAI,WAAW,EAEzCA,IACEA,IAAc,OAChBnB,GAAA,EAEAE,GAAwBtE,EAAO,KAAK,MAAM,IAAI,EAGpD,CAAC,CACH,CClHA,IAAI,aAAa,IAAI,4BAA6B,IAAM,CACtD,IAAI,MAAM,OAAO,cAAgBX,EACjC,IAAI,uBAAuB,cAAgBmD,GAE3CuB,GAAA,EACAsB,GAAA,EAEArB,EAAAA,OAAOwB,EAAiB,UAAW,oBAAqB,SAAUzE,EAAO,CACvEA,EAAM,IAAI,gBAAiB,CACzB,KAAM,gBACN,KAAM,qBACN,MAAO,IAAI,WAAW,MACpB,4DAAA,CACF,CACD,CACH,CAAC,EAEDiD,EAAAA,OAAOyB,EAAc,qBAAsB,CAAC1E,EAAOrB,IAAS,CAC1D,MAAMgG,EAAuB,IAAI,MAAM,UAAU,sBAAsB,EAEvE,GAAG,IAAI,QAAQ,MAAQA,EAAqB,CAC1C,MAAMzC,EAAgB,IAAI,QAAQ,KAAK,GAAA,EACjCgB,EAAevE,EAAK,GAAA,EAEvBuD,IAAgBgB,GACjBlD,EAAM,IAAI,gBAAiBS,EAAO,UAAU,CACxC,KAAM,oBACN,QAAS,IAAM,IAAI,MAAM,KAAKe,EAAoB,CAAC,KAAA7C,EAAK,CAAA,EACvD,IAAI,WAAW,MAAM,gDAAgD,CAAC,CAAA,CAG/E,CACF,CAAC,EAEDsE,EAAAA,OAAOsB,EAAgB,UAAW,QAAS,SAAUvE,EAAO,CACrD,IAAI,QAAQ,MAIjBA,EAAM,IACJ,gBACAS,EAAO,UACL,CACE,KAAM,oBACN,QAAS,IAAM,CACb,IAAI,MAAM,KAAKe,CAAkB,CACnC,CAAA,EAEF,IAAI,WAAW,MAAM,gDAAgD,CAAA,EAEvE,EAAA,CAEJ,CAAC,CACH,CAAC"}