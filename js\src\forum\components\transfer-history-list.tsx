import Component from 'flarum/Component';
import app from 'flarum/app';
import Button from 'flarum/components/Button';
import LoadingIndicator from 'flarum/components/LoadingIndicator';
import Placeholder from 'flarum/components/Placeholder';
import ItemList from 'flarum/utils/ItemList';
import TransferHistoryListItem from './transfer-history-list-item';
import type Mithril from 'mithril';

export default class TransferHistoryList extends Component<{ params: { user: unknown } }> {
  loading!: boolean;
  moreResults!: boolean;
  transferHistory!: unknown[];
  user!: unknown;

  oninit(vnode: Mithril.Vnode<unknown, this>): void {
    super.oninit(vnode);

    this.loading = false;
    this.moreResults = false;
    this.transferHistory = [];
    this.user = this.attrs.params.user;

    this.loadResults();
  }

  view(): Mithril.Children {
    if (this.loading) {
      return LoadingIndicator.component();
    }

    const transferHistoryItems = this.transferHistory.map((transferHistory) => (
      <li style="padding-top:5px" key={(transferHistory as any).id()} data-id={(transferHistory as any).id()}>
        {TransferHistoryListItem.component({ transferHistory })}
      </li>
    ));

    return (
      <div className="TransferHistoryList">
        <div className="TransferHistoryList-header">
          <h3>{app.translator.trans('wusong8899-transfer-money.forum.transfer-history')}</h3>
        </div>

        <div className="TransferHistoryList-content">
          {!this.loading && this.transferHistory.length === 0 && (
            <div>
              {Placeholder.component({
                text: app.translator.trans('wusong8899-transfer-money.forum.transfer-history-empty'),
              })}
            </div>
          )}

          {this.transferHistory.length > 0 && (
            <ul style="margin: 0;padding: 0;list-style-type: none;position: relative;">
              {transferHistoryItems}
            </ul>
          )}

          {this.hasMoreResults() && (
            <div className="TransferHistoryList-loadMore">
              {Button.component(
                {
                  className: 'Button',
                  onclick: this.loadMore.bind(this),
                },
                app.translator.trans('wusong8899-transfer-money.forum.load-more')
              )}
            </div>
          )}
        </div>
      </div>
    );
  }

  loadMore(): void {
    this.loading = true;

    this.loadResults(this.transferHistory.length)
      .catch(() => {})
      .then(this.parseResults.bind(this));
  }

  parseResults(results: unknown): void {
    this.moreResults = !!(results as any).payload.links && !!(results as any).payload.links.next;

    [].push.apply(this.transferHistory, app.store.all('transferMoney'));

    this.loading = false;
    m.redraw();
  }

  hasMoreResults(): boolean {
    return this.moreResults;
  }

  loadResults(offset = 0): Promise<unknown> {
    return app.store
      .find('transferMoney', {
        filter: {
          user: (this.user as any).id(),
        },
        page: {
          offset,
          limit: 20,
        },
      })
      .catch(() => {})
      .then(this.parseResults.bind(this));
  }
}
